{"version": 3, "file": "scope.js", "sourceRoot": "", "sources": ["../../../lib/compile/codegen/scope.ts"], "names": [], "mappings": ";;;AAAA,iCAAyC;AAezC,MAAM,UAAW,SAAQ,KAAK;IAE5B,YAAY,IAAoB;QAC9B,KAAK,CAAC,uBAAuB,IAAI,cAAc,CAAC,CAAA;QAChD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;IACzB,CAAC;CACF;AAuBD,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,yDAAO,CAAA;IACP,6DAAS,CAAA;AACX,CAAC,EAHW,cAAc,8BAAd,cAAc,QAGzB;AAMY,QAAA,QAAQ,GAAG;IACtB,KAAK,EAAE,IAAI,WAAI,CAAC,OAAO,CAAC;IACxB,GAAG,EAAE,IAAI,WAAI,CAAC,KAAK,CAAC;IACpB,GAAG,EAAE,IAAI,WAAI,CAAC,KAAK,CAAC;CACrB,CAAA;AAED,MAAa,KAAK;IAKhB,YAAY,EAAC,QAAQ,EAAE,MAAM,KAAkB,EAAE;QAJ9B,WAAM,GAAqC,EAAE,CAAA;QAK9D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QACzB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;IACvB,CAAC;IAED,MAAM,CAAC,YAA2B;QAChC,OAAO,YAAY,YAAY,WAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IAC9E,CAAC;IAED,IAAI,CAAC,MAAc;QACjB,OAAO,IAAI,WAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;IACxC,CAAC;IAES,QAAQ,CAAC,MAAc;QAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QACzD,OAAO,GAAG,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,CAAA;IACjC,CAAC;IAEO,UAAU,CAAC,MAAc;;QAC/B,IAAI,CAAA,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,SAAS,0CAAE,GAAG,CAAC,MAAM,CAAC,KAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YAC5F,MAAM,IAAI,KAAK,CAAC,oBAAoB,MAAM,gCAAgC,CAAC,CAAA;QAC7E,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAC,MAAM,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,CAAA;IACnD,CAAC;CACF;AA7BD,sBA6BC;AAOD,MAAa,cAAe,SAAQ,WAAI;IAKtC,YAAY,MAAc,EAAE,OAAe;QACzC,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED,QAAQ,CAAC,KAAgB,EAAE,EAAC,QAAQ,EAAE,SAAS,EAAY;QACzD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,SAAS,GAAG,IAAA,QAAC,EAAA,IAAI,IAAI,WAAI,CAAC,QAAQ,CAAC,IAAI,SAAS,GAAG,CAAA;IAC1D,CAAC;CACF;AAdD,wCAcC;AAMD,MAAM,IAAI,GAAG,IAAA,QAAC,EAAA,IAAI,CAAA;AAElB,MAAa,UAAW,SAAQ,KAAK;IAKnC,YAAY,IAAuB;QACjC,KAAK,CAAC,IAAI,CAAC,CAAA;QALM,YAAO,GAAgB,EAAE,CAAA;QAM1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,IAAI,GAAG,EAAC,GAAG,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAG,EAAC,CAAA;IACpD,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,IAAI,CAAC,MAAc;QACjB,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED,KAAK,CAAC,YAAqC,EAAE,KAAgB;;QAC3D,IAAI,KAAK,CAAC,GAAG,KAAK,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACpF,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAmB,CAAA;QACxD,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAA;QACrB,MAAM,QAAQ,GAAG,MAAA,KAAK,CAAC,GAAG,mCAAI,KAAK,CAAC,GAAG,CAAA;QACvC,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,EAAE,EAAE,CAAC;YACP,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC9B,IAAI,KAAK;gBAAE,OAAO,KAAK,CAAA;QACzB,CAAC;aAAM,CAAC;YACN,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,EAAE,CAAA;QACvC,CAAC;QACD,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QAEtB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAA;QAC3D,MAAM,SAAS,GAAG,CAAC,CAAC,MAAM,CAAA;QAC1B,CAAC,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,CAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAC,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,QAAQ,CAAC,MAAc,EAAE,QAAiB;QACxC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC/B,IAAI,CAAC,EAAE;YAAE,OAAM;QACf,OAAO,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACzB,CAAC;IAED,SAAS,CAAC,SAAe,EAAE,SAAuC,IAAI,CAAC,OAAO;QAC5E,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,IAAoB,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;gBAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,CAAA;YACzF,OAAO,IAAA,QAAC,EAAA,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QACzC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,SAAS,CACP,SAAuC,IAAI,CAAC,OAAO,EACnD,UAA4B,EAC5B,OAAiD;QAEjD,OAAO,IAAI,CAAC,aAAa,CACvB,MAAM,EACN,CAAC,IAAoB,EAAE,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS;gBAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,CAAA;YACrF,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;QACxB,CAAC,EACD,UAAU,EACV,OAAO,CACR,CAAA;IACH,CAAC;IAEO,aAAa,CACnB,MAAoC,EACpC,SAAkD,EAClD,aAA8B,EAAE,EAChC,OAAiD;QAEjD,IAAI,IAAI,GAAS,UAAG,CAAA;QACpB,KAAK,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;YAC5B,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;YACzB,IAAI,CAAC,EAAE;gBAAE,SAAQ;YACjB,MAAM,OAAO,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAA;YACtE,EAAE,CAAC,OAAO,CAAC,CAAC,IAAoB,EAAE,EAAE;gBAClC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;oBAAE,OAAM;gBAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,OAAO,CAAC,CAAA;gBACzC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;gBACvB,IAAI,CAAC,EAAE,CAAC;oBACN,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAQ,CAAC,KAAK,CAAA;oBACzD,IAAI,GAAG,IAAA,QAAC,EAAA,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAA;gBACxD,CAAC;qBAAM,IAAI,CAAC,CAAC,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,IAAI,CAAC,CAAC,EAAE,CAAC;oBACjC,IAAI,GAAG,IAAA,QAAC,EAAA,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAA;gBACtC,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;gBAC5B,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,CAAA;YAC7C,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAjGD,gCAiGC"}