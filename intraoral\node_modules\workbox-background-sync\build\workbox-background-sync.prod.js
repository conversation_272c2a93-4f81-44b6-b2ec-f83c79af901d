this.workbox=this.workbox||{},this.workbox.backgroundSync=function(t,e,s,n){"use strict";function r(){return r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var s=arguments[e];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(t[n]=s[n])}return t},r.apply(this,arguments)}const a=(t,e)=>e.some((e=>t instanceof e));let i,c;const o=new WeakMap,u=new WeakMap,h=new WeakMap,y=new WeakMap,w=new WeakMap;let f={get(t,e,s){if(t instanceof IDBTransaction){if("done"===e)return u.get(t);if("objectStoreNames"===e)return t.objectStoreNames||h.get(t);if("store"===e)return s.objectStoreNames[1]?void 0:s.objectStore(s.objectStoreNames[0])}return m(t[e])},set:(t,e,s)=>(t[e]=s,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function l(t){return t!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(c||(c=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(p(this),e),m(o.get(this))}:function(...e){return m(t.apply(p(this),e))}:function(e,...s){const n=t.call(p(this),e,...s);return h.set(n,e.sort?e.sort():[e]),m(n)}}function d(t){return"function"==typeof t?l(t):(t instanceof IDBTransaction&&function(t){if(u.has(t))return;const e=new Promise(((e,s)=>{const n=()=>{t.removeEventListener("complete",r),t.removeEventListener("error",a),t.removeEventListener("abort",a)},r=()=>{e(),n()},a=()=>{s(t.error||new DOMException("AbortError","AbortError")),n()};t.addEventListener("complete",r),t.addEventListener("error",a),t.addEventListener("abort",a)}));u.set(t,e)}(t),a(t,i||(i=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction]))?new Proxy(t,f):t)}function m(t){if(t instanceof IDBRequest)return function(t){const e=new Promise(((e,s)=>{const n=()=>{t.removeEventListener("success",r),t.removeEventListener("error",a)},r=()=>{e(m(t.result)),n()},a=()=>{s(t.error),n()};t.addEventListener("success",r),t.addEventListener("error",a)}));return e.then((e=>{e instanceof IDBCursor&&o.set(e,t)})).catch((()=>{})),w.set(e,t),e}(t);if(y.has(t))return y.get(t);const e=d(t);return e!==t&&(y.set(t,e),w.set(e,t)),e}const p=t=>w.get(t);const g=["get","getKey","getAll","getAllKeys","count"],D=["put","add","delete","clear"],b=new Map;function B(t,e){if(!(t instanceof IDBDatabase)||e in t||"string"!=typeof e)return;if(b.get(e))return b.get(e);const s=e.replace(/FromIndex$/,""),n=e!==s,r=D.includes(s);if(!(s in(n?IDBIndex:IDBObjectStore).prototype)||!r&&!g.includes(s))return;const a=async function(t,...e){const a=this.transaction(t,r?"readwrite":"readonly");let i=a.store;return n&&(i=i.index(e.shift())),(await Promise.all([i[s](...e),r&&a.done]))[0]};return b.set(e,a),a}f=(t=>r({},t,{get:(e,s,n)=>B(e,s)||t.get(e,s,n),has:(e,s)=>!!B(e,s)||t.has(e,s)}))(f);try{self["workbox:background-sync:6.5.4"]&&_()}catch(t){}const I="requests",q="queueName";class k{constructor(){this.t=null}async addEntry(t){const e=(await this.getDb()).transaction(I,"readwrite",{durability:"relaxed"});await e.store.add(t),await e.done}async getFirstEntryId(){const t=await this.getDb(),e=await t.transaction(I).store.openCursor();return null==e?void 0:e.value.id}async getAllEntriesByQueueName(t){const e=await this.getDb(),s=await e.getAllFromIndex(I,q,IDBKeyRange.only(t));return s||new Array}async getEntryCountByQueueName(t){return(await this.getDb()).countFromIndex(I,q,IDBKeyRange.only(t))}async deleteEntry(t){const e=await this.getDb();await e.delete(I,t)}async getFirstEntryByQueueName(t){return await this.getEndEntryFromIndex(IDBKeyRange.only(t),"next")}async getLastEntryByQueueName(t){return await this.getEndEntryFromIndex(IDBKeyRange.only(t),"prev")}async getEndEntryFromIndex(t,e){const s=await this.getDb(),n=await s.transaction(I).store.index(q).openCursor(t,e);return null==n?void 0:n.value}async getDb(){return this.t||(this.t=await function(t,e,{blocked:s,upgrade:n,blocking:r,terminated:a}={}){const i=indexedDB.open(t,e),c=m(i);return n&&i.addEventListener("upgradeneeded",(t=>{n(m(i.result),t.oldVersion,t.newVersion,m(i.transaction))})),s&&i.addEventListener("blocked",(()=>s())),c.then((t=>{a&&t.addEventListener("close",(()=>a())),r&&t.addEventListener("versionchange",(()=>r()))})).catch((()=>{})),c}("workbox-background-sync",3,{upgrade:this.i})),this.t}i(t,e){e>0&&e<3&&t.objectStoreNames.contains(I)&&t.deleteObjectStore(I);t.createObjectStore(I,{autoIncrement:!0,keyPath:"id"}).createIndex(q,q,{unique:!1})}}class E{constructor(t){this.o=t,this.u=new k}async pushEntry(t){delete t.id,t.queueName=this.o,await this.u.addEntry(t)}async unshiftEntry(t){const e=await this.u.getFirstEntryId();e?t.id=e-1:delete t.id,t.queueName=this.o,await this.u.addEntry(t)}async popEntry(){return this.h(await this.u.getLastEntryByQueueName(this.o))}async shiftEntry(){return this.h(await this.u.getFirstEntryByQueueName(this.o))}async getAll(){return await this.u.getAllEntriesByQueueName(this.o)}async size(){return await this.u.getEntryCountByQueueName(this.o)}async deleteEntry(t){await this.u.deleteEntry(t)}async h(t){return t&&await this.deleteEntry(t.id),t}}const R=["method","referrer","referrerPolicy","mode","credentials","cache","redirect","integrity","keepalive"];class x{static async fromRequest(t){const e={url:t.url,headers:{}};"GET"!==t.method&&(e.body=await t.clone().arrayBuffer());for(const[s,n]of t.headers.entries())e.headers[s]=n;for(const s of R)void 0!==t[s]&&(e[s]=t[s]);return new x(e)}constructor(t){"navigate"===t.mode&&(t.mode="same-origin"),this.l=t}toObject(){const t=Object.assign({},this.l);return t.headers=Object.assign({},this.l.headers),t.body&&(t.body=t.body.slice(0)),t}toRequest(){return new Request(this.l.url,this.l)}clone(){return new x(this.toObject())}}const v="workbox-background-sync",j=new Set,S=t=>{const e={request:new x(t.requestData).toRequest(),timestamp:t.timestamp};return t.metadata&&(e.metadata=t.metadata),e};class A{constructor(t,{forceSyncFallback:s,onSync:n,maxRetentionTime:r}={}){if(this.m=!1,this.p=!1,j.has(t))throw new e.WorkboxError("duplicate-queue-name",{name:t});j.add(t),this.g=t,this.D=n||this.replayRequests,this.B=r||10080,this.I=Boolean(s),this.q=new E(this.g),this.k()}get name(){return this.g}async pushRequest(t){await this.R(t,"push")}async unshiftRequest(t){await this.R(t,"unshift")}async popRequest(){return this.v("pop")}async shiftRequest(){return this.v("shift")}async getAll(){const t=await this.q.getAll(),e=Date.now(),s=[];for(const n of t){const t=60*this.B*1e3;e-n.timestamp>t?await this.q.deleteEntry(n.id):s.push(S(n))}return s}async size(){return await this.q.size()}async R({request:t,metadata:e,timestamp:s=Date.now()},n){const r={requestData:(await x.fromRequest(t.clone())).toObject(),timestamp:s};switch(e&&(r.metadata=e),n){case"push":await this.q.pushEntry(r);break;case"unshift":await this.q.unshiftEntry(r)}this.m?this.p=!0:await this.registerSync()}async v(t){const e=Date.now();let s;switch(t){case"pop":s=await this.q.popEntry();break;case"shift":s=await this.q.shiftEntry()}if(s){const n=60*this.B*1e3;return e-s.timestamp>n?this.v(t):S(s)}}async replayRequests(){let t;for(;t=await this.shiftRequest();)try{await fetch(t.request.clone())}catch(s){throw await this.unshiftRequest(t),new e.WorkboxError("queue-replay-failed",{name:this.g})}}async registerSync(){if("sync"in self.registration&&!this.I)try{await self.registration.sync.register(`${v}:${this.g}`)}catch(t){}}k(){"sync"in self.registration&&!this.I?self.addEventListener("sync",(t=>{if(t.tag===`${v}:${this.g}`){const e=async()=>{let e;this.m=!0;try{await this.D({queue:this})}catch(t){if(t instanceof Error)throw e=t,e}finally{!this.p||e&&!t.lastChance||await this.registerSync(),this.m=!1,this.p=!1}};t.waitUntil(e())}})):this.D({queue:this})}static get j(){return j}}return t.BackgroundSyncPlugin=class{constructor(t,e){this.fetchDidFail=async({request:t})=>{await this.S.pushRequest({request:t})},this.S=new A(t,e)}},t.Queue=A,t.QueueStore=E,t.StorableRequest=x,t}({},workbox.core._private,workbox.core._private,workbox.core._private);
//# sourceMappingURL=workbox-background-sync.prod.js.map
